# 监控体系重构与参数管理设计方案 (v2.1)

## 1. 整体架构设计

### 1.1. 监控体系架构

为了构建一个健壮、可扩展且易于维护的监控体系，我们将引入业界标准实践，设计如下统一监控框架。

- **标准化**: 全面拥抱 Prometheus 指标格式，确保与主流开源监控系统（Grafana, Alertmanager）的无缝集成。
- **可观测性三要素**: 整合指标（Metrics）、日志（Logging）和追踪（Tracing），提供全方位的系统洞察。
- **解耦与中心化**: 监控管理中心与具体业务模块解耦，通过统一API网关进行通信，易于扩展和维护。

```
┌─────────────────────────────────────────────────────────────┐
│                    监控管理中心 (Monitoring & Management Center) │
├─────────────────────────────────────────────────────────────┤
│  Dashboard & 可视化 (Grafana)   │  配置管理 (Nacos)          │
├─────────────────────────────────────────────────────────────┤
│  统一告警平台 (Alertmanager)  │  日志聚合分析 (Loki)         │
├─────────────────────────────────────────────────────────────┤
│  分布式追踪 (Tempo/Jaeger)    │  统一监控API网关             │
├─────────────────────────────────────────────────────────────┤
│  OCR监控    │  转换器监控  │  插件监控  │  系统资源监控      │
└─────────────────────────────────────────────────────────────┘
```

### 1.2. 参数中心设计

- **统一管理**: 所有模块的配置参数由参数中心（Nacos）统一管理，支持动态更新和版本控制。
- **高可用**: 采用分布式架构，确保参数服务的高可用性。
- **安全性**: 对敏感参数进行加密存储，并提供基于角色的访问控制（RBAC）。

## 2. 详细技术设计

### 2.1. 监控数据采集

- **指标采集 (Metrics)**:
  - **实现方式**: 各Java模块通过集成 `micrometer.io` 库来暴露符合Prometheus格式的指标端点 (`/actuator/prometheus`)。
- **日志采集 (Logging)**:
  - **实现方式**: 使用 `Logback` 或 `Log4j2` 配合 `Loki` 的Java客户端，将结构化日志（JSON格式）推送到Loki。
  - **日志内容**: 包含 `traceId` 和 `spanId`，以便与分布式追踪关联。
- **分布式追踪 (Tracing)**:
  - **实现方式**: 集成 `OpenTelemetry` SDK，自动或手动创建 `Span`，通过OTLP Exporter将追踪数据发送到 `Tempo` 或 `Jaeger`。

### 2.2. 指标设计与管理

#### 2.2.1. 指标命名规范

遵循 Prometheus 的命名规范: `subsystem_module_metric_name{label1="value1", label2="value2"}`

- **subsystem**: 系统名称，例如 `doc_converter`。
- **module**: 模块名称，例如 `ocr`, `transformer`, `plugin`。
- **metric_name**: 指标名称，例如 `requests_total`, `request_duration_seconds`。
- **labels**: 维度标签，用于区分不同场景，例如 `status="success"`, `type="pdf"`。

#### 2.2.2. 核心业务指标

| 指标名称                       | 类型      | 标签                       | 描述                 |
| ------------------------------ | --------- | -------------------------- | -------------------- |
| `requests_total`             | Counter   | `module`, `status`     | 处理的总请求数       |
| `request_duration_seconds`   | Histogram | `module`, `quantile`   | 请求处理耗时分布     |
| `active_requests`            | Gauge     | `module`                 | 当前正在处理的请求数 |
| `cache_hit_ratio`            | Gauge     | `cache_name`             | 缓存命中率           |
| `thread_pool_active_threads` | Gauge     | `pool_name`              | 线程池活跃线程数     |
| `error_rate`                 | Gauge     | `module`, `error_type` | 错误率               |

#### 2.2.3. 系统资源指标

| 指标名称                  | 类型      | 描述                |
| ------------------------- | --------- | ------------------- |
| `jvm_memory_used_bytes` | Gauge     | JVM内存使用情况     |
| `jvm_gc_pause_seconds`  | Histogram | JVM垃圾回收暂停时间 |
| `cpu_usage_percent`     | Gauge     | CPU使用率           |
| `disk_space_free_bytes` | Gauge     | 磁盘剩余空间        |

#### 2.2.4. 指标生命周期管理
1. **自动归档**: 对30天内未被查询的指标自动降级到低成本存储。
2. **基数监控**: 对高基数指标（label组合>10,000）进行告警。
3. **价值评估**: 季度性评审指标使用率，清理无用指标。

### 2.3. 健康检查机制

- **检查级别**:
  - **Liveness (存活探针)**: 快速检查服务进程是否仍在运行。用于容器编排系统的自动重启。
  - **Readiness (就绪探针)**: 检查服务是否准备好接收流量，例如依赖的数据库、缓存是否连接正常。用于控制流量接入。
- **健康检查API**: `GET /actuator/health` (利用Spring Boot Actuator的默认端点)
  - **Liveness**: `GET /actuator/health/liveness` -> `{"status": "UP"}`
  - **Readiness**: `GET /actuator/health/readiness` -> `{"status": "UP", "components": {"db": {"status": "UP"}, "redis": {"status": "UP"}}}`

### 2.4. 参数中心实现

- **技术选型**: 采用Nacos作为集中式配置中心。
- **核心功能**:
  - **版本控制与回滚**: 所有配置变更都有版本记录，支持一键回滚。
  - **灰度发布**: 支持按实例、按策略进行配置的灰度发布。
  - **权限管理**: 对不同环境和模块的配置进行精细化的权限控制。
  - **实时推送**: 配置变更后，通过长连接实时推送到目标服务实例。
- **高可用方案**:
  - **数据库集群**: Nacos依赖的数据库采用高可用集群。
  - **本地缓存**: 客户端缓存配置副本，配置中心不可用时使用本地缓存。
  - **配置快照**: 服务启动时下载全量配置快照，避免运行时依赖配置中心。

### 2.5. 监控告警集成

- **Prometheus配置**:
  - `prometheus.yml`: 配置 `scrape_configs`，动态发现和抓取各模块的指标端点。
  - `alerting_rules.yml`: 定义告警规则，例如 `jvm_memory_used_percent > 85%`。
- **Alertmanager配置**:
  - `alertmanager.yml`: 配置告警路由（`route`）、接收人（`receivers`）和通知模板。
  - **告警渠道**: 支持邮件、Slack、Teams、钉钉等多种通知方式。

## 3. 安全性考虑

- **访问控制**:
  - **API网关认证**: 所有对监控API的访问都必须经过API网关的认证和授权。
  - **Dashboard权限**: 监控Dashboard（如Grafana）应集成LDAP或OAuth2，实现单点登录和基于角色的访问控制。
- **数据安全**:
  - **传输加密**: 监控数据在传输过程中应使用TLS加密。
  - **敏感信息脱敏**: 日志和配置中的密码、密钥等敏感信息必须进行脱敏或加密存储。
- **防止攻击**:
  - **API速率限制**: 对监控API设置合理的速率限制，防止被恶意请求拖垮。
  - **指标注入防护**: 严格校验指标的标签，防止恶意用户通过注入大量高基数标签导致监控系统崩溃。

## 4. 实施计划与任务拆分

| 阶段 | 周数 | 核心任务 | 负责人 | 产出物 |
| :--- | :--- | :--- | :--- | :--- |
| **第一阶段：基础设施搭建** | 1-2 | - 部署Prometheus, Grafana, Loki, Tempo, Alertmanager<br>- 搭建Nacos配置中心<br>- 初始化Git仓库和CI/CD流水线 | 张三 | - 可用的监控和参数管理基础设施<br>- API文档 |
| **第二阶段：核心模块接入** | 3-4 | - 核心服务集成Micrometer, OpenTelemetry<br>- 配置基础JVM和业务指标<br>- 将硬编码配置迁移至参数中心 | 李四 | - 核心服务可观测<br>- Grafana监控大盘v1.0 |
| **第三阶段：完善监控与告警** | 5-6 | - 接入所有转换器和插件模块到新监控体系<br>- 配置核心告警规则 (Alertmanager)<br>- 建立告警通知渠道 (Email, Slack/Teams) | 王五 | - 全模块监控覆盖<br>- 自动化告警通知 |
| **第四阶段：上线与优化** | 7 | - 全量上线<br>- 性能压测与调优<br>- 编写用户手册和运维文档 | 赵六 | - 稳定的生产系统<br>- 完整的项目文档 |
| **第五阶段：高级功能 (持续)** | - | - 引入分布式追踪，实现全链路调用分析<br>- 探索基于机器学习的异常检测和预测性告警<br>- 实现基于服务依赖图的根因分析系统 | - | - 高级运维能力 |

## 5. 风险评估与应对

| 风险点            | 可能性 | 影响 | 应对措施                                                         |
| ----------------- | ------ | ---- | ---------------------------------------------------------------- |
| 技术选型不当      | 中     | 高   | 进行充分的技术预研和PoC验证，选择社区成熟、活跃的方案。          |
| 监控系统自身故障  | 低     | 高   | 对监控系统本身进行高可用部署和监控。                             |
| 指标爆炸 (高基数) | 中     | 中   | 制定严格的指标和标签规范，定期审查和清理无用指标。               |
| 迁移过程影响业务  | 高     | 中   | 采用灰度迁移方案，新旧系统并行一段时间，确保稳定后再下线旧系统。 |
| 配置迁移故障      | 高     | 高   | 采用双写机制：新老配置系统并行运行1周，验证一致性后再切换。        |
| 存储成本失控      | 中     | 高   | 设置数据保留策略：核心指标保留30天，原始日志保留7天。              |

## 6. 总结

本方案在原有设计的基础上，全面拥抱云原生和开源生态，构建了一个集指标、日志、追踪于一体的现代化、高可用的可观测性平台。通过标准化的组件、分阶段的实施计划和充分的风险评估，该方案不仅能满足当前对系统全面监控和动态管理的需求，也为未来的系统扩展和智能化运维打下了坚实的基础。
