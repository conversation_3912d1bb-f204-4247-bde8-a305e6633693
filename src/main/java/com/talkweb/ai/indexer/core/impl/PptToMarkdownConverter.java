package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.indexer.core.converter.ConversionCapabilities;
import com.talkweb.ai.indexer.core.converter.ConversionContext;
import com.talkweb.ai.indexer.core.converter.ConversionMetadata;
import com.talkweb.ai.indexer.util.ppt.PptConversionConfig;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Set;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * PowerPoint to Markdown converter implementation using the new architecture
 * 
 * This converter wraps the existing utility converter and provides
 * the new interface while maintaining backward compatibility.
 * 
 * Features:
 * - Full compatibility with PPT, PPTX, and PPTM formats
 * - Comprehensive slide content extraction (text, images, tables, charts)
 * - Structure preservation with proper Markdown hierarchy
 * - Configurable conversion options
 * - High-performance processing with memory optimization
 * - Robust error handling and recovery mechanisms
 * - Support for speaker notes and slide metadata
 * 
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class PptToMarkdownConverter extends AbstractDocumentConverter {
    
    private static final Logger logger = Logger.getLogger(PptToMarkdownConverter.class.getName());
    
    /**
     * Default constructor
     */
    public PptToMarkdownConverter() {
        super();
    }
    
    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("PowerPoint to Markdown Converter")
                .description("Converts PowerPoint files (.ppt, .pptx, .pptm) to Markdown format with comprehensive slide content extraction")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("ppt", "pptx", "pptm"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }
    
    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("ppt", "pptx", "pptm");
    }
    
    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.LISTS)
                .feature(ConversionCapabilities.Features.IMAGES)
                .feature(ConversionCapabilities.Features.METADATA)
                .capability("speakerNotes", true)
                .capability("slideNumbers", true)
                .capability("hiddenSlides", true)
                .capability("charts", true)
                .capability("strictMode", true)
                .capability("looseMode", true)
                .capability("imageExtraction", true)
                .capability("maxFileSize", 500 * 1024 * 1024) // 500MB
                .build();
    }
    
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        logger.info("Starting PowerPoint conversion for: " + inputFile.getName());
        
        String parentDir = inputFile.getParent();
        if (parentDir == null) {
            parentDir = ".";
        }
        Path outputPath = Path.of(parentDir, inputFile.getName().replaceAll("\\.(ppt|pptx|pptm)$", ".md"));
        
        try {
            // Create conversion configuration from context
            PptConversionConfig config = createConfigFromContext(context);
            
            // Use the existing utility converter
            String markdownContent = com.talkweb.ai.indexer.util.PptToMarkdownConverter.convertToMarkdown(inputFile, config);
            
            return new ConversionResult(
                ConversionResult.Status.SUCCESS,
                inputFile.getPath(),
                outputPath.toString(),
                markdownContent
            );
            
        } catch (IOException e) {
            logger.log(Level.SEVERE, "PowerPoint conversion failed: " + inputFile.getName(), e);
            throw new ConversionException("PowerPoint conversion failed: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Unexpected error during PowerPoint conversion: " + inputFile.getName(), e);
            throw new ConversionException("Unexpected error during PowerPoint conversion: " + e.getMessage(), e);
        }
    }
    
    /**
     * Creates PptConversionConfig from ConversionContext
     */
    private PptConversionConfig createConfigFromContext(ConversionContext context) {
        PptConversionConfig config = new PptConversionConfig();
        
        // Apply boolean options
        config.setIncludeMetadata(context.getOptions().getBooleanOption("includeMetadata", true));
        config.setIncludeSpeakerNotes(context.getOptions().getBooleanOption("includeSpeakerNotes", false));
        config.setExtractImages(context.getOptions().getBooleanOption("extractImages", true));
        config.setExtractTables(context.getOptions().getBooleanOption("extractTables", true));
        config.setExtractCharts(context.getOptions().getBooleanOption("extractCharts", false));
        config.setIncludeSlideNumbers(context.getOptions().getBooleanOption("includeSlideNumbers", true));
        config.setIncludeHiddenSlides(context.getOptions().getBooleanOption("includeHiddenSlides", false));
        config.setStrictMode(context.getOptions().getBooleanOption("strictMode", false));
        config.setPreserveFormatting(context.getOptions().getBooleanOption("preserveFormatting", true));
        config.setGenerateTableOfContents(context.getOptions().getBooleanOption("generateTableOfContents", false));
        config.setNormalizeWhitespace(context.getOptions().getBooleanOption("normalizeWhitespace", true));
        config.setConvertBulletsToMarkdown(context.getOptions().getBooleanOption("convertBulletsToMarkdown", true));
        config.setPreserveTextBoxes(context.getOptions().getBooleanOption("preserveTextBoxes", true));
        config.setExtractTextFromShapes(context.getOptions().getBooleanOption("extractTextFromShapes", true));
        config.setEnableCaching(context.getOptions().getBooleanOption("enableCaching", true));
        config.setStreamProcessing(context.getOptions().getBooleanOption("streamProcessing", false));
        config.setSkipCorruptedSlides(context.getOptions().getBooleanOption("skipCorruptedSlides", true));
        config.setLogDetailedErrors(context.getOptions().getBooleanOption("logDetailedErrors", true));
        config.setGenerateErrorReport(context.getOptions().getBooleanOption("generateErrorReport", false));
        
        // Apply string options
        config.setImageOutputDirectory(context.getOptions().getStringOption("imageOutputDirectory", "images"));
        config.setImageFormat(context.getOptions().getStringOption("imageFormat", "png"));
        
        // Apply integer options
        config.setMaxImageWidth(context.getOptions().getIntOption("maxImageWidth", 800));
        config.setMaxImageHeight(context.getOptions().getIntOption("maxImageHeight", 600));
        config.setMaxCacheSize(context.getOptions().getIntOption("maxCacheSize", 100));
        config.setProcessingTimeout(context.getOptions().getIntOption("processingTimeout", 300));
        
        return config;
    }
    
    /**
     * Validates PowerPoint file format
     */
    @Override
    protected void validateInput(File inputFile, ConversionContext context) throws ConversionException {
        super.validateInput(inputFile, context);
        
        String fileName = inputFile.getName().toLowerCase();
        if (!fileName.endsWith(".ppt") && !fileName.endsWith(".pptx") && !fileName.endsWith(".pptm")) {
            throw new ConversionException("Unsupported file format. Only .ppt, .pptx, and .pptm files are supported.");
        }
        
        // Additional validation for file size
        long fileSize = inputFile.length();
        long maxSize = getCapabilities().getCapability("maxFileSize", 500L * 1024 * 1024);
        
        if (fileSize > maxSize) {
            throw new ConversionException("File size (" + fileSize + " bytes) exceeds maximum allowed size (" + maxSize + " bytes)");
        }
        
        // Check if file is readable
        if (!inputFile.canRead()) {
            throw new ConversionException("Cannot read input file: " + inputFile.getPath());
        }
    }
    
    /**
     * Gets conversion statistics
     */
    public ConversionStatistics getLastConversionStatistics() {
        // This would be implemented to return statistics from the last conversion
        // For now, return a placeholder
        return new ConversionStatistics();
    }
    
    /**
     * Conversion statistics holder
     */
    public static class ConversionStatistics {
        private int slideCount = 0;
        private int imageCount = 0;
        private int tableCount = 0;
        private int chartCount = 0;
        private int speakerNoteCount = 0;
        private long processingTimeMs = 0;
        
        // Getters and setters
        public int getSlideCount() { return slideCount; }
        public void setSlideCount(int slideCount) { this.slideCount = slideCount; }
        
        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }
        
        public int getTableCount() { return tableCount; }
        public void setTableCount(int tableCount) { this.tableCount = tableCount; }
        
        public int getChartCount() { return chartCount; }
        public void setChartCount(int chartCount) { this.chartCount = chartCount; }
        
        public int getSpeakerNoteCount() { return speakerNoteCount; }
        public void setSpeakerNoteCount(int speakerNoteCount) { this.speakerNoteCount = speakerNoteCount; }
        
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
        
        @Override
        public String toString() {
            return String.format("ConversionStatistics{slides=%d, images=%d, tables=%d, charts=%d, speakerNotes=%d, time=%dms}",
                    slideCount, imageCount, tableCount, chartCount, speakerNoteCount, processingTimeMs);
        }
    }
}
