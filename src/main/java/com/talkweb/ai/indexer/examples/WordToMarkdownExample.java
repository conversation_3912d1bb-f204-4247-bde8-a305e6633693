package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.util.WordToMarkdownConverter;
import com.talkweb.ai.indexer.util.word.WordConversionMode;
import com.talkweb.ai.indexer.util.word.WordConversionOptions;
import org.apache.poi.xwpf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Example demonstrating Word to Markdown conversion capabilities
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordToMarkdownExample {
    
    private static final Logger logger = LoggerFactory.getLogger(WordToMarkdownExample.class);
    
    public static void main(String[] args) {
        try {
            // Create a sample Word document
            File sampleDoc = createSampleWordDocument();
            
            // Example 1: Basic conversion
            basicConversionExample(sampleDoc);
            
            // Example 2: High-fidelity conversion
            highFidelityConversionExample(sampleDoc);
            
            // Example 3: Custom options conversion
            customOptionsConversionExample(sampleDoc);
            
            // Example 4: Strict mode conversion
            strictModeConversionExample(sampleDoc);
            
            // Clean up
            sampleDoc.delete();
            
            logger.info("All examples completed successfully!");
            
        } catch (Exception e) {
            logger.error("Example execution failed", e);
        }
    }
    
    /**
     * Example 1: Basic conversion with default settings
     */
    private static void basicConversionExample(File wordFile) throws IOException {
        logger.info("=== Basic Conversion Example ===");
        
        String markdown = WordToMarkdownConverter.convert(wordFile);
        
        logger.info("Converted markdown:\n{}", markdown);
        
        // Save to file
        Files.write(Paths.get("basic_output.md"), markdown.getBytes());
        logger.info("Saved to: basic_output.md");
    }
    
    /**
     * Example 2: High-fidelity conversion
     */
    private static void highFidelityConversionExample(File wordFile) throws IOException {
        logger.info("\n=== High-Fidelity Conversion Example ===");
        
        WordConversionOptions options = WordConversionOptions.highFidelity();
        String markdown = WordToMarkdownConverter.convert(wordFile, options);
        
        logger.info("High-fidelity markdown:\n{}", markdown);
        
        // Save to file
        Files.write(Paths.get("high_fidelity_output.md"), markdown.getBytes());
        logger.info("Saved to: high_fidelity_output.md");
    }
    
    /**
     * Example 3: Custom options conversion
     */
    private static void customOptionsConversionExample(File wordFile) throws IOException {
        logger.info("\n=== Custom Options Conversion Example ===");
        
        WordConversionOptions options = new WordConversionOptions()
                .setExtractImages(true)
                .setImageOutputDir("extracted_images/")
                .setPreserveTableFormatting(true)
                .setConvertFootnotes(true)
                .setPreserveTextFormatting(true)
                .setSkipEmptyParagraphs(true);
        
        String markdown = WordToMarkdownConverter.convert(wordFile, WordConversionMode.LOOSE, options);
        
        logger.info("Custom options markdown:\n{}", markdown);
        logger.info("Options used: {}", options);
        
        // Save to file
        Files.write(Paths.get("custom_options_output.md"), markdown.getBytes());
        logger.info("Saved to: custom_options_output.md");
    }
    
    /**
     * Example 4: Strict mode conversion
     */
    private static void strictModeConversionExample(File wordFile) throws IOException {
        logger.info("\n=== Strict Mode Conversion Example ===");
        
        try {
            String markdown = WordToMarkdownConverter.convert(wordFile, WordConversionMode.STRICT);
            
            logger.info("Strict mode markdown:\n{}", markdown);
            
            // Save to file
            Files.write(Paths.get("strict_mode_output.md"), markdown.getBytes());
            logger.info("Saved to: strict_mode_output.md");
            
        } catch (Exception e) {
            logger.warn("Strict mode conversion failed (this is expected for some documents): {}", e.getMessage());
            
            // Fallback to loose mode
            logger.info("Falling back to loose mode...");
            String markdown = WordToMarkdownConverter.convert(wordFile, WordConversionMode.LOOSE);
            Files.write(Paths.get("fallback_output.md"), markdown.getBytes());
            logger.info("Fallback conversion saved to: fallback_output.md");
        }
    }
    
    /**
     * Creates a sample Word document for demonstration
     */
    private static File createSampleWordDocument() throws IOException {
        File tempFile = File.createTempFile("sample_word_doc", ".docx");
        
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(tempFile)) {
            
            // Add title
            XWPFParagraph title = document.createParagraph();
            title.setStyle("Title");
            XWPFRun titleRun = title.createRun();
            titleRun.setText("Word to Markdown Conversion Demo");
            titleRun.setBold(true);
            titleRun.setFontSize(20);
            
            // Add heading 1
            XWPFParagraph h1 = document.createParagraph();
            h1.setStyle("Heading1");
            XWPFRun h1Run = h1.createRun();
            h1Run.setText("Introduction");
            h1Run.setBold(true);
            h1Run.setFontSize(16);
            
            // Add paragraph with formatting
            XWPFParagraph para1 = document.createParagraph();
            XWPFRun run1 = para1.createRun();
            run1.setText("This document demonstrates the ");
            
            XWPFRun run2 = para1.createRun();
            run2.setText("Word to Markdown");
            run2.setBold(true);
            
            XWPFRun run3 = para1.createRun();
            run3.setText(" conversion capabilities. It supports various formatting including ");
            
            XWPFRun run4 = para1.createRun();
            run4.setText("italic text");
            run4.setItalic(true);
            
            XWPFRun run5 = para1.createRun();
            run5.setText(", ");
            
            XWPFRun run6 = para1.createRun();
            run6.setText("bold text");
            run6.setBold(true);
            
            XWPFRun run7 = para1.createRun();
            run7.setText(", and ");
            
            XWPFRun run8 = para1.createRun();
            run8.setText("code snippets");
            run8.setFontFamily("Courier New");
            
            XWPFRun run9 = para1.createRun();
            run9.setText(".");
            
            // Add heading 2
            XWPFParagraph h2 = document.createParagraph();
            h2.setStyle("Heading2");
            XWPFRun h2Run = h2.createRun();
            h2Run.setText("Features");
            h2Run.setBold(true);
            h2Run.setFontSize(14);
            
            // Add list items (simulated as paragraphs)
            XWPFParagraph listItem1 = document.createParagraph();
            XWPFRun listRun1 = listItem1.createRun();
            listRun1.setText("• Support for both .doc and .docx formats");
            
            XWPFParagraph listItem2 = document.createParagraph();
            XWPFRun listRun2 = listItem2.createRun();
            listRun2.setText("• Preservation of text formatting");
            
            XWPFParagraph listItem3 = document.createParagraph();
            XWPFRun listRun3 = listItem3.createRun();
            listRun3.setText("• Table conversion support");
            
            XWPFParagraph listItem4 = document.createParagraph();
            XWPFRun listRun4 = listItem4.createRun();
            listRun4.setText("• Image extraction capabilities");
            
            // Add table
            XWPFTable table = document.createTable(3, 3);
            
            // Header row
            XWPFTableRow headerRow = table.getRow(0);
            headerRow.getCell(0).setText("Feature");
            headerRow.getCell(1).setText("Supported");
            headerRow.getCell(2).setText("Notes");
            
            // Data rows
            XWPFTableRow row1 = table.getRow(1);
            row1.getCell(0).setText("Text Formatting");
            row1.getCell(1).setText("Yes");
            row1.getCell(2).setText("Bold, italic, code");
            
            XWPFTableRow row2 = table.getRow(2);
            row2.getCell(0).setText("Images");
            row2.getCell(1).setText("Yes");
            row2.getCell(2).setText("Extracted to files");
            
            // Add conclusion
            XWPFParagraph conclusion = document.createParagraph();
            XWPFRun conclusionRun = conclusion.createRun();
            conclusionRun.setText("This converter provides a comprehensive solution for converting Word documents to Markdown format while preserving document structure and formatting.");
            
            document.write(out);
        }
        
        logger.info("Created sample Word document: {}", tempFile.getAbsolutePath());
        return tempFile;
    }
}
