package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.impl.ExcelToMarkdownConverter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Simple test for Excel converter debugging
 */
public class SimpleExcelTest {

    public static void main(String[] args) {
        try {
            // Create a simple test file
            File testFile = createSimpleExcelFile();
            System.out.println("Created test file: " + testFile.getAbsolutePath());
            
            // Create converter
            ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter();
            
            System.out.println("Converting file...");
            ConversionResult result = converter.convert(testFile);
            
            if (result.getStatus() == ConversionResult.Status.SUCCESS) {
                System.out.println("✓ Conversion successful!");
                System.out.println("Content:");
                System.out.println(result.getContent());
            } else {
                System.out.println("✗ Conversion failed: " + result.getStatus());
            }
            
            // Clean up
            testFile.delete();
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static File createSimpleExcelFile() throws IOException {
        File file = new File("simple_test.xlsx");
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Test");
            
            // Create a simple 2x2 table
            Row row1 = sheet.createRow(0);
            row1.createCell(0).setCellValue("Name");
            row1.createCell(1).setCellValue("Value");
            
            Row row2 = sheet.createRow(1);
            row2.createCell(0).setCellValue("Test");
            row2.createCell(1).setCellValue(123);
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
}
