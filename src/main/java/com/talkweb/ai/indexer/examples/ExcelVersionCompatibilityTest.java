package com.talkweb.ai.indexer.examples;

import com.talkweb.ai.indexer.core.ConversionResult;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.impl.ExcelToMarkdownConverter;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;

/**
 * 测试Excel转换器对不同版本Excel文件的兼容性
 * 
 * 支持的格式：
 * - .xls (Excel 97-2003)
 * - .xlsx (Excel 2007+)
 * - .xlsm (Excel 2007+ with macros)
 */
public class ExcelVersionCompatibilityTest {

    public static void main(String[] args) {
        try {
            System.out.println("=== Excel版本兼容性测试 ===\n");
            
            // 创建转换器
            ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter();
            
            // 测试支持的文件扩展名
            testSupportedExtensions(converter);
            
            // 测试 .xls 格式 (Excel 97-2003)
            System.out.println("1. 测试 .xls 格式 (Excel 97-2003):");
            File xlsFile = createXlsFile();
            testConversion(converter, xlsFile, "XLS");
            
            // 测试 .xlsx 格式 (Excel 2007+)
            System.out.println("\n2. 测试 .xlsx 格式 (Excel 2007+):");
            File xlsxFile = createXlsxFile();
            testConversion(converter, xlsxFile, "XLSX");
            
            // 测试复杂的 .xlsx 文件
            System.out.println("\n3. 测试复杂的 .xlsx 文件 (合并单元格、公式):");
            File complexXlsxFile = createComplexXlsxFile();
            testConversion(converter, complexXlsxFile, "复杂XLSX");
            
            // 测试复杂的 .xls 文件
            System.out.println("\n4. 测试复杂的 .xls 文件 (合并单元格、公式):");
            File complexXlsFile = createComplexXlsFile();
            testConversion(converter, complexXlsFile, "复杂XLS");
            
            // 显示缓存统计
            System.out.println("\n5. 缓存统计:");
            showCacheStatistics();
            
            // 清理文件
            cleanupFiles(xlsFile, xlsxFile, complexXlsxFile, complexXlsFile);
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSupportedExtensions(ExcelToMarkdownConverter converter) {
        System.out.println("支持的文件扩展名测试:");
        
        String[] extensions = {"xls", "xlsx", "xlsm", "XLS", "XLSX", "XLSM", "csv", "txt", null};
        for (String ext : extensions) {
            boolean supported = converter.supportsExtension(ext);
            String status = supported ? "✓" : "✗";
            System.out.println("  " + status + " " + (ext != null ? ext : "null") + ": " + supported);
        }
        System.out.println();
    }
    
    private static void testConversion(ExcelToMarkdownConverter converter, File file, String type) {
        try {
            System.out.println("转换文件: " + file.getName() + " (" + type + ")");
            
            long startTime = System.currentTimeMillis();
            ConversionResult result = converter.convert(file);
            long endTime = System.currentTimeMillis();
            
            if (result.getStatus() == ConversionResult.Status.SUCCESS) {
                System.out.println("✓ 转换成功 (耗时: " + (endTime - startTime) + "ms)");
                
                String content = result.getContent();
                System.out.println("内容预览 (前150字符):");
                String preview = content.length() > 150 ? content.substring(0, 150) + "..." : content;
                System.out.println(preview.replace("\n", "\\n"));
                
                // 验证关键内容
                validateContent(content, type);
                
            } else {
                System.out.println("✗ 转换失败: " + result.getStatus());
            }
        } catch (Exception e) {
            System.out.println("✗ 转换异常: " + e.getMessage());
        }
    }
    
    private static void validateContent(String content, String type) {
        System.out.println("内容验证:");
        
        // 检查基本结构
        boolean hasTitle = content.contains("#");
        boolean hasFileInfo = content.contains("**文件信息:**");
        boolean hasTable = content.contains("|");
        boolean hasTableSeparator = content.contains("---|");
        
        System.out.println("  - 标题: " + (hasTitle ? "✓" : "✗"));
        System.out.println("  - 文件信息: " + (hasFileInfo ? "✓" : "✗"));
        System.out.println("  - 表格: " + (hasTable ? "✓" : "✗"));
        System.out.println("  - 表格分隔符: " + (hasTableSeparator ? "✓" : "✗"));
        
        // 检查特定内容
        if (type.contains("复杂")) {
            boolean hasMergedContent = content.contains("合并标题") || content.contains("总计");
            boolean hasFormulaResult = content.contains("300") || content.contains("150"); // 公式计算结果
            System.out.println("  - 合并单元格内容: " + (hasMergedContent ? "✓" : "✗"));
            System.out.println("  - 公式计算结果: " + (hasFormulaResult ? "✓" : "✗"));
        }
    }
    
    private static void showCacheStatistics() {
        var stats = ExcelToMarkdownConverter.getCacheStatistics();
        System.out.println("缓存统计信息:");
        for (var entry : stats.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
    }
    
    private static File createXlsFile() throws IOException {
        File file = new File("test_compatibility.xls");
        
        try (Workbook workbook = new HSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("XLS测试");
            
            // 创建表头
            Row header = sheet.createRow(0);
            header.createCell(0).setCellValue("产品名称");
            header.createCell(1).setCellValue("数量");
            header.createCell(2).setCellValue("单价");
            header.createCell(3).setCellValue("总价");
            
            // 创建数据行
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("产品A");
            row1.createCell(1).setCellValue(10);
            row1.createCell(2).setCellValue(25.5);
            row1.createCell(3).setCellValue(255.0);
            
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("产品B");
            row2.createCell(1).setCellValue(5);
            row2.createCell(2).setCellValue(30.0);
            row2.createCell(3).setCellValue(150.0);
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
    
    private static File createXlsxFile() throws IOException {
        File file = new File("test_compatibility.xlsx");
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("XLSX测试");
            
            // 创建表头
            Row header = sheet.createRow(0);
            header.createCell(0).setCellValue("员工姓名");
            header.createCell(1).setCellValue("部门");
            header.createCell(2).setCellValue("入职日期");
            header.createCell(3).setCellValue("薪资");
            
            // 创建数据行
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("张三");
            row1.createCell(1).setCellValue("技术部");
            row1.createCell(2).setCellValue(new Date());
            row1.createCell(3).setCellValue(8000.0);
            
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("李四");
            row2.createCell(1).setCellValue("销售部");
            row2.createCell(2).setCellValue(new Date());
            row2.createCell(3).setCellValue(6000.0);
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
    
    private static File createComplexXlsxFile() throws IOException {
        File file = new File("complex_test.xlsx");
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("复杂XLSX");
            
            // 创建合并的标题
            Row titleRow = sheet.createRow(0);
            titleRow.createCell(0).setCellValue("销售报表 - 2024年第一季度");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            
            // 创建表头
            Row header = sheet.createRow(2);
            header.createCell(0).setCellValue("产品");
            header.createCell(1).setCellValue("数量");
            header.createCell(2).setCellValue("单价");
            header.createCell(3).setCellValue("总价");
            
            // 创建数据行
            Row row1 = sheet.createRow(3);
            row1.createCell(0).setCellValue("产品X");
            row1.createCell(1).setCellValue(10);
            row1.createCell(2).setCellValue(15.0);
            row1.createCell(3).setCellFormula("B4*C4"); // 公式
            
            Row row2 = sheet.createRow(4);
            row2.createCell(0).setCellValue("产品Y");
            row2.createCell(1).setCellValue(5);
            row2.createCell(2).setCellValue(30.0);
            row2.createCell(3).setCellFormula("B5*C5"); // 公式
            
            // 总计行
            Row totalRow = sheet.createRow(5);
            totalRow.createCell(0).setCellValue("总计");
            totalRow.createCell(3).setCellFormula("SUM(D4:D5)"); // 求和公式
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
    
    private static File createComplexXlsFile() throws IOException {
        File file = new File("complex_test.xls");
        
        try (Workbook workbook = new HSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("复杂XLS");
            
            // 创建合并的标题
            Row titleRow = sheet.createRow(0);
            titleRow.createCell(0).setCellValue("财务报表 - 2024年");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            
            // 创建表头
            Row header = sheet.createRow(2);
            header.createCell(0).setCellValue("项目");
            header.createCell(1).setCellValue("金额");
            header.createCell(2).setCellValue("百分比");
            
            // 创建数据行
            Row row1 = sheet.createRow(3);
            row1.createCell(0).setCellValue("收入");
            row1.createCell(1).setCellValue(100000);
            row1.createCell(2).setCellFormula("B4/200000*100"); // 百分比公式
            
            Row row2 = sheet.createRow(4);
            row2.createCell(0).setCellValue("支出");
            row2.createCell(1).setCellValue(80000);
            row2.createCell(2).setCellFormula("B5/200000*100"); // 百分比公式
            
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
        
        return file;
    }
    
    private static void cleanupFiles(File... files) {
        for (File file : files) {
            if (file.exists()) {
                file.delete();
            }
        }
    }
}
