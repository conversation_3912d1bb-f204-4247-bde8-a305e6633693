package com.talkweb.ai.indexer.util.word;

/**
 * Word文档转换选项
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordConversionOptions {
    
    private WordConversionMode mode = WordConversionMode.STANDARD;
    private boolean preserveFormatting = true;
    private boolean includeImages = true;
    private boolean includeTables = true;
    private boolean includeHeaders = true;
    private boolean includeFooters = false;
    private boolean normalizeWhitespace = true;
    private boolean convertTables = true;
    private boolean convertImages = true;
    private boolean convertFootnotes = false;
    private boolean extractImages = false;
    private String imageDirectory = "images";
    private String imageOutputDir = "images";
    private int maxImageSize = 1024 * 1024; // 1MB
    private int maxImageWidth = 800;
    private String imageFormat = "png";
    private boolean preserveTextFormatting = true;
    private boolean skipEmptyParagraphs = false;
    private boolean preserveTableFormatting = true;
    private boolean generateImageAltText = true;
    
    public WordConversionOptions() {
    }
    
    public static WordConversionOptions createDefault() {
        return new WordConversionOptions();
    }
    
    public static WordConversionOptions createMinimal() {
        WordConversionOptions options = new WordConversionOptions();
        options.setPreserveFormatting(false);
        options.setIncludeImages(false);
        options.setIncludeHeaders(false);
        options.setIncludeFooters(false);
        return options;
    }
    
    public static WordConversionOptions createComplete() {
        WordConversionOptions options = new WordConversionOptions();
        options.setIncludeHeaders(true);
        options.setIncludeFooters(true);
        options.setIncludeImages(true);
        options.setIncludeTables(true);
        return options;
    }

    public static WordConversionOptions basic() {
        return createMinimal();
    }

    public static WordConversionOptions highFidelity() {
        return createComplete();
    }
    
    // Getters and setters
    public WordConversionMode getMode() {
        return mode;
    }
    
    public void setMode(WordConversionMode mode) {
        this.mode = mode;
    }
    
    public boolean isPreserveFormatting() {
        return preserveFormatting;
    }
    
    public void setPreserveFormatting(boolean preserveFormatting) {
        this.preserveFormatting = preserveFormatting;
    }
    
    public boolean isIncludeImages() {
        return includeImages;
    }
    
    public void setIncludeImages(boolean includeImages) {
        this.includeImages = includeImages;
    }
    
    public boolean isIncludeTables() {
        return includeTables;
    }
    
    public void setIncludeTables(boolean includeTables) {
        this.includeTables = includeTables;
    }
    
    public boolean isIncludeHeaders() {
        return includeHeaders;
    }
    
    public void setIncludeHeaders(boolean includeHeaders) {
        this.includeHeaders = includeHeaders;
    }
    
    public boolean isIncludeFooters() {
        return includeFooters;
    }
    
    public void setIncludeFooters(boolean includeFooters) {
        this.includeFooters = includeFooters;
    }
    
    public boolean isNormalizeWhitespace() {
        return normalizeWhitespace;
    }
    
    public void setNormalizeWhitespace(boolean normalizeWhitespace) {
        this.normalizeWhitespace = normalizeWhitespace;
    }
    
    public int getMaxImageWidth() {
        return maxImageWidth;
    }
    
    public void setMaxImageWidth(int maxImageWidth) {
        this.maxImageWidth = maxImageWidth;
    }
    
    public String getImageFormat() {
        return imageFormat;
    }
    
    public void setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat;
    }

    public boolean isConvertTables() {
        return convertTables;
    }

    public void setConvertTables(boolean convertTables) {
        this.convertTables = convertTables;
    }

    public boolean isConvertImages() {
        return convertImages;
    }

    public void setConvertImages(boolean convertImages) {
        this.convertImages = convertImages;
    }

    public boolean isConvertFootnotes() {
        return convertFootnotes;
    }

    public void setConvertFootnotes(boolean convertFootnotes) {
        this.convertFootnotes = convertFootnotes;
    }

    public boolean isExtractImages() {
        return extractImages;
    }

    public void setExtractImages(boolean extractImages) {
        this.extractImages = extractImages;
    }

    public String getImageDirectory() {
        return imageDirectory;
    }

    public void setImageDirectory(String imageDirectory) {
        this.imageDirectory = imageDirectory;
    }

    public int getMaxImageSize() {
        return maxImageSize;
    }

    public void setMaxImageSize(int maxImageSize) {
        this.maxImageSize = maxImageSize;
    }

    // Fluent API methods for chaining
    public WordConversionOptions setPreserveTableFormatting(boolean preserve) {
        this.includeTables = preserve;
        return this;
    }

    public WordConversionOptions setPreserveListFormatting(boolean preserve) {
        // Add list formatting field if needed
        return this;
    }

    public WordConversionOptions setPreserveTextFormatting(boolean preserve) {
        this.preserveFormatting = preserve;
        return this;
    }

    public WordConversionOptions setSkipEmptyParagraphs(boolean skip) {
        this.skipEmptyParagraphs = skip;
        return this;
    }

    public boolean isPreserveTextFormatting() {
        return preserveTextFormatting;
    }

    public boolean isSkipEmptyParagraphs() {
        return skipEmptyParagraphs;
    }

    public boolean isPreserveTableFormatting() {
        return preserveTableFormatting;
    }

    public boolean isGenerateImageAltText() {
        return generateImageAltText;
    }

    public String getImageOutputDir() {
        return imageOutputDir;
    }

    public WordConversionOptions setImageOutputDir(String imageOutputDir) {
        this.imageOutputDir = imageOutputDir;
        return this;
    }
}
