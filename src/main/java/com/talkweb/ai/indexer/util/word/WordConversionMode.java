package com.talkweb.ai.indexer.util.word;

/**
 * Word文档转换模式
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public enum WordConversionMode {
    
    /**
     * 标准模式 - 平衡质量和性能
     */
    STANDARD("标准模式", "平衡质量和性能的转换模式"),
    
    /**
     * 快速模式 - 优先性能
     */
    FAST("快速模式", "优先性能的转换模式，可能牺牲部分质量"),
    
    /**
     * 高质量模式 - 优先质量
     */
    HIGH_QUALITY("高质量模式", "优先质量的转换模式，可能较慢"),
    
    /**
     * 兼容模式 - 最大兼容性
     */
    COMPATIBLE("兼容模式", "最大兼容性的转换模式，适用于复杂文档"),

    /**
     * 宽松模式 - 容错处理
     */
    LOOSE("宽松模式", "容错处理的转换模式，忽略部分错误"),

    /**
     * 严格模式 - 严格处理
     */
    STRICT("严格模式", "严格处理的转换模式，遇到错误即停止");
    
    private final String displayName;
    private final String description;
    
    WordConversionMode(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
