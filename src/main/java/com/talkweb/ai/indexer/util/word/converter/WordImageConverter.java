package com.talkweb.ai.indexer.util.word.converter;

import com.talkweb.ai.indexer.util.markdown.MarkdownBuilder;
import com.talkweb.ai.indexer.util.word.WordConversionContext;
// WordConversionOptions is now in the word package
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.openxml4j.opc.PackagePart;
import org.apache.poi.openxml4j.opc.PackageRelationship;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * Converter for Word images to Markdown
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
public class WordImageConverter implements WordElementConverter<XWPFRun> {
    
    private static final Logger logger = LoggerFactory.getLogger(WordImageConverter.class);
    
    @Override
    public boolean canConvert(XWPFRun element, WordConversionContext context) {
        return element != null && hasImages(element);
    }
    
    @Override
    public void convert(XWPFRun element, MarkdownBuilder builder, WordConversionContext context) 
            throws ConversionException {
        
        validate(element, context);
        
        if (!context.getOptions().isExtractImages()) {
            // Skip image extraction, just add placeholder
            builder.append("![Image]()");
            return;
        }
        
        try {
            List<XWPFPicture> pictures = element.getEmbeddedPictures();
            for (XWPFPicture picture : pictures) {
                convertPicture(picture, builder, context);
            }
        } catch (Exception e) {
            logger.warn("Failed to convert image in run", e);
            if (context.isStrictMode()) {
                throw new ConversionException("Failed to convert image", e);
            }
            // In loose mode, add placeholder
            builder.append("![Image conversion failed]()");
        }
    }
    
    /**
     * Converts a single picture
     */
    private void convertPicture(XWPFPicture picture, MarkdownBuilder builder, 
                               WordConversionContext context) throws ConversionException {
        
        try {
            // Get picture data
            XWPFPictureData pictureData = picture.getPictureData();
            if (pictureData == null) {
                logger.warn("Picture data is null");
                builder.append("![Image]()");
                return;
            }
            
            // Generate filename
            String originalExtension = getFileExtension(pictureData.getPackagePart().getPartName().getName());
            String targetExtension = getTargetExtension(originalExtension, context.getOptions());
            String fileName = context.generateImageName(targetExtension);
            
            // Create output directory
            String imageDir = context.getOptions().getImageOutputDir();
            Path outputDir = getImageOutputPath(context, imageDir);
            Files.createDirectories(outputDir);
            
            // Save image file
            Path imagePath = outputDir.resolve(fileName);
            byte[] imageData = pictureData.getData();
            
            // Process image if needed
            imageData = processImageData(imageData, originalExtension, targetExtension, context.getOptions());
            
            Files.write(imagePath, imageData);
            
            // Generate alt text
            String altText = generateAltText(picture, context);
            
            // Create relative path for markdown
            String relativePath = imageDir + fileName;
            
            // Add to markdown
            builder.append("![").append(altText).append("](").append(relativePath).append(")");
            
            // Map image for reference
            context.mapImage(pictureData.getChecksum() + "", fileName);
            
            logger.debug("Extracted image: {}", fileName);
            
        } catch (IOException e) {
            logger.error("Failed to extract image", e);
            if (context.isStrictMode()) {
                throw new ConversionException("Failed to extract image", e);
            }
            builder.append("![Image extraction failed]()");
        }
    }
    
    /**
     * Checks if run contains images
     */
    private boolean hasImages(XWPFRun run) {
        return run.getEmbeddedPictures() != null && !run.getEmbeddedPictures().isEmpty();
    }
    
    /**
     * Gets file extension from filename
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "png"; // Default extension
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * Gets target extension based on options
     */
    private String getTargetExtension(String originalExtension, com.talkweb.ai.indexer.util.word.WordConversionOptions options) {
        // For now, just return original extension since the new WordConversionOptions doesn't have ImageFormat enum
        // TODO: Add image format configuration to the new WordConversionOptions if needed
        return originalExtension;
    }
    
    /**
     * Gets the output path for images
     */
    private Path getImageOutputPath(WordConversionContext context, String imageDir) {
        File sourceFile = context.getSourceFile();
        if (sourceFile != null && sourceFile.getParent() != null) {
            return Paths.get(sourceFile.getParent(), imageDir);
        }
        return Paths.get(imageDir);
    }
    
    /**
     * Processes image data (resize, convert format, etc.)
     */
    private byte[] processImageData(byte[] originalData, String originalExtension,
                                   String targetExtension, com.talkweb.ai.indexer.util.word.WordConversionOptions options) {
        
        // For now, just return original data
        // In a full implementation, you could use ImageIO to resize/convert
        // This would require additional image processing libraries
        
        if (originalExtension.equals(targetExtension)) {
            return originalData;
        }
        
        // TODO: Implement image format conversion and resizing
        // This would require libraries like ImageIO, BufferedImage, etc.
        
        return originalData;
    }
    
    /**
     * Generates alt text for the image
     */
    private String generateAltText(XWPFPicture picture, WordConversionContext context) {
        if (!context.getOptions().isGenerateImageAltText()) {
            return "Image";
        }
        
        try {
            // Try to get description from picture
            String description = picture.getDescription();
            if (description != null && !description.trim().isEmpty()) {
                return description.trim();
            }
            
            // Try to get filename
            XWPFPictureData pictureData = picture.getPictureData();
            if (pictureData != null) {
                String fileName = pictureData.getFileName();
                if (fileName != null && !fileName.trim().isEmpty()) {
                    // Remove extension and clean up
                    String name = fileName;
                    if (name.contains(".")) {
                        name = name.substring(0, name.lastIndexOf("."));
                    }
                    return name.replaceAll("[_-]", " ").trim();
                }
            }
            
        } catch (Exception e) {
            logger.debug("Failed to generate alt text", e);
        }
        
        return "Image";
    }
    
    /**
     * Extracts image from HWPF document (for .doc files)
     * This is more complex and would require additional implementation
     */
    private void convertHWPFImage(Object element, MarkdownBuilder builder, 
                                 WordConversionContext context) throws ConversionException {
        
        // HWPF image extraction is more complex
        // For now, just add placeholder
        logger.warn("HWPF image extraction not fully implemented");
        builder.append("![Image]()");
    }
    
    @Override
    public int getPriority() {
        return 90; // High priority for images
    }
    
    @Override
    public boolean shouldProcessChildren(XWPFRun element, WordConversionContext context) {
        return false; // We handle the image ourselves
    }
}
