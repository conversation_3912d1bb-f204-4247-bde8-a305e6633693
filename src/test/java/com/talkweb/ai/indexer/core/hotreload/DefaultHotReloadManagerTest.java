package com.talkweb.ai.indexer.core.hotreload;

import com.talkweb.ai.indexer.core.PluginManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import com.talkweb.ai.indexer.core.hotreload.HotReloadListener.FileChangeType;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * DefaultHotReloadManager测试
 */
public class DefaultHotReloadManagerTest {

    @Mock
    private PluginManager pluginManager;

    private DefaultHotReloadManager hotReloadManager;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        hotReloadManager = new DefaultHotReloadManager(pluginManager);
    }

    @Test
    void testStartAndStop() throws Exception {
        // Arrange
        Path watchDir = tempDir.resolve("plugins");
        Files.createDirectories(watchDir);

        // Act
        hotReloadManager.start(List.of(watchDir));

        // Assert
        assertTrue(hotReloadManager.isRunning());
        assertTrue(hotReloadManager.getWatchDirectories().contains(watchDir));

        // Act - stop
        hotReloadManager.stop();

        // Assert
        assertFalse(hotReloadManager.isRunning());
        assertTrue(hotReloadManager.getWatchDirectories().isEmpty());
    }

    @Test
    void testAddAndRemoveWatchDirectory() throws Exception {
        // Arrange
        Path watchDir1 = tempDir.resolve("plugins1");
        Path watchDir2 = tempDir.resolve("plugins2");
        Files.createDirectories(watchDir1);
        Files.createDirectories(watchDir2);

        hotReloadManager.start(List.of(watchDir1));

        // Act
        hotReloadManager.addWatchDirectory(watchDir2);

        // Assert
        Set<Path> watchDirs = hotReloadManager.getWatchDirectories();
        assertEquals(2, watchDirs.size());
        assertTrue(watchDirs.contains(watchDir1));
        assertTrue(watchDirs.contains(watchDir2));

        // Act - remove
        hotReloadManager.removeWatchDirectory(watchDir2);

        // Assert
        watchDirs = hotReloadManager.getWatchDirectories();
        assertEquals(1, watchDirs.size());
        assertTrue(watchDirs.contains(watchDir1));
        assertFalse(watchDirs.contains(watchDir2));

        hotReloadManager.stop();
    }

    @Test
    void testAddWatchDirectoryNonExistent() {
        // Arrange
        Path nonExistentDir = tempDir.resolve("non-existent");

        // Act & Assert
        assertThrows(HotReloadException.class, () -> {
            hotReloadManager.addWatchDirectory(nonExistentDir);
        });
    }

    @Test
    void testSetAndGetDebounceDelay() {
        // Act
        hotReloadManager.setDebounceDelay(1000);

        // Assert
        assertEquals(1000, hotReloadManager.getDebounceDelay());

        // Act - negative value should be set to 0
        hotReloadManager.setDebounceDelay(-100);

        // Assert
        assertEquals(0, hotReloadManager.getDebounceDelay());
    }

    @Test
    void testSetAndGetSupportedExtensions() {
        // Arrange
        Set<String> extensions = Set.of(".jar", ".zip", ".war");

        // Act
        hotReloadManager.setSupportedExtensions(extensions);

        // Assert
        assertEquals(extensions, hotReloadManager.getSupportedExtensions());
    }

    @Test
    void testTriggerReload() throws Exception {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.createFile(pluginFile);

        // Act
        hotReloadManager.triggerReload(pluginFile);

        // Assert
        verify(pluginManager).installPlugin(eq(pluginFile), eq(true));
    }

    @Test
    void testTriggerReloadInvalidFile() throws Exception {
        // Arrange
        Path invalidFile = tempDir.resolve("test.txt");
        Files.createFile(invalidFile);

        // Act & Assert
        assertThrows(HotReloadException.class, () -> {
            hotReloadManager.triggerReload(invalidFile);
        });
    }

    @Test
    void testAddAndRemoveListener() throws Exception {
        // Arrange
        CountDownLatch latch = new CountDownLatch(1);
        HotReloadListener listener = new HotReloadListener() {
            @Override
            public void onWatchStarted(List<Path> watchDirectories) {
                latch.countDown();
            }
        };

        // Act
        hotReloadManager.addHotReloadListener(listener);

        Path watchDir = tempDir.resolve("plugins");
        Files.createDirectories(watchDir);
        hotReloadManager.start(List.of(watchDir));

        // Assert
        assertTrue(latch.await(1, TimeUnit.SECONDS));

        // Act - remove listener
        hotReloadManager.removeHotReloadListener(listener);
        hotReloadManager.stop();
    }

    @Test
    void testStatistics() throws Exception {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.createFile(pluginFile);

        // Act
        hotReloadManager.triggerReload(pluginFile);

        // Assert
        HotReloadManager.HotReloadStatistics stats = hotReloadManager.getStatistics();
        assertEquals(1, stats.getTotalReloads());
        assertEquals(1, stats.getSuccessfulReloads());
        assertEquals(0, stats.getFailedReloads());
        assertEquals(100.0, stats.getSuccessRate(), 0.01);

        // Act - clear statistics
        hotReloadManager.clearStatistics();

        // Assert
        stats = hotReloadManager.getStatistics();
        assertEquals(0, stats.getTotalReloads());
        assertEquals(0, stats.getSuccessfulReloads());
        assertEquals(0, stats.getFailedReloads());
    }

    @Test
    void testReloadFailure() throws Exception {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.createFile(pluginFile);

        doThrow(new RuntimeException("Plugin load failed"))
            .when(pluginManager).installPlugin(any(), eq(true));

        CountDownLatch failureLatch = new CountDownLatch(1);
        HotReloadListener listener = new HotReloadListener() {
            @Override
            public void onReloadFailure(Path pluginPath, Throwable error) {
                failureLatch.countDown();
            }
        };

        hotReloadManager.addHotReloadListener(listener);

        // Act & Assert
        assertThrows(HotReloadException.class, () -> {
            hotReloadManager.triggerReload(pluginFile);
        });

        // Verify statistics
        HotReloadManager.HotReloadStatistics stats = hotReloadManager.getStatistics();
        assertEquals(1, stats.getTotalReloads());
        assertEquals(0, stats.getSuccessfulReloads());
        assertEquals(1, stats.getFailedReloads());
        assertEquals(0.0, stats.getSuccessRate(), 0.01);
    }

    @Test
    void testListenerBeforeReloadVeto() throws Exception {
        // Arrange
        Path pluginFile = tempDir.resolve("test-plugin.jar");
        Files.createFile(pluginFile);

        HotReloadListener vetoListener = new HotReloadListener() {
            @Override
            public boolean beforeReload(Path pluginPath) {
                return false; // 拒绝重载
            }
        };

        hotReloadManager.addHotReloadListener(vetoListener);

        // Act
        hotReloadManager.triggerReload(pluginFile);

        // Assert
        verify(pluginManager, never()).installPlugin(any(), anyBoolean());
    }

    @Test
    void testClose() throws Exception {
        // Arrange
        Path watchDir = tempDir.resolve("plugins");
        Files.createDirectories(watchDir);
        hotReloadManager.start(List.of(watchDir));

        // Act
        hotReloadManager.close();

        // Assert
        assertFalse(hotReloadManager.isRunning());
    }

    @Test
    void testFileChangeDetection() throws Exception {
        // Arrange
        Path watchDir = tempDir.resolve("plugins");
        Files.createDirectories(watchDir);

        CountDownLatch changeLatch = new CountDownLatch(1);
        AtomicReference<Path> detectedPath = new AtomicReference<>();
        AtomicReference<FileChangeType> detectedType = new AtomicReference<>();

        HotReloadListener listener = new HotReloadListener() {
            @Override
            public void onFileChanged(Path filePath, FileChangeType changeType) {
                detectedPath.set(filePath);
                detectedType.set(changeType);
                if (changeType == FileChangeType.CREATED) {
                    changeLatch.countDown();
                }
            }
        };

        hotReloadManager.addHotReloadListener(listener);
        hotReloadManager.setDebounceDelay(50); // 减少延迟以加快测试
        hotReloadManager.start(List.of(watchDir));

        // Give the watch service time to initialize
        Thread.sleep(100);

        // Act
        Path pluginFile = watchDir.resolve("new-plugin.jar");
        Files.createFile(pluginFile);

        // Force file system sync on some platforms
        Files.write(pluginFile, "test content".getBytes());

        // Assert
        boolean detected = changeLatch.await(5, TimeUnit.SECONDS);

        if (!detected) {
            // Debug information
            System.err.println("File change not detected. Debug info:");
            System.err.println("Watch directories: " + hotReloadManager.getWatchDirectories());
            System.err.println("Supported extensions: " + hotReloadManager.getSupportedExtensions());
            System.err.println("Plugin file exists: " + Files.exists(pluginFile));
            System.err.println("Plugin file name: " + pluginFile.getFileName());
            System.err.println("Detected path: " + detectedPath.get());
            System.err.println("Detected type: " + detectedType.get());
        }

        assertTrue(detected, "File change detection failed - see debug output above");

        hotReloadManager.stop();
    }
}
